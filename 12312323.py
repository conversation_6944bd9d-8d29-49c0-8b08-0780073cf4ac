import nodriver as uc

async def main():
    # 配置代理
    proxy = {
        'http': 'http://127.0.0.1:1080',
        'https': 'http://127.0.0.1:1080'
    }
    browser = await uc.start(proxy=proxy)  # 启动浏览器，自动伪造指纹并使用代理
    page = await browser.get('https://example.com')  # 导航页面
    # 伪造更多指纹（如自定义User-Agent）
    await page.evaluate('navigator.userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";')
    await browser.sleep(50)  # 模拟人类延迟
    # await browser.close()

if __name__ == '__main__':
    uc.loop().run_until_complete(main())